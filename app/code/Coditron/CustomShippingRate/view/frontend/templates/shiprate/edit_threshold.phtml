<?php
/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
$helper = $block->getMpHelper();
$isPartner = $helper->isSeller();
$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';

$sellerId = $block->getSellerId();
$shipRate = $block->getShipRate();

$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
?>
<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
        enctype="multipart/form-data" method="post" id="form-save-threshold"
        data-mage-init='{"validation":{}}'>
            <div class="fieldset wk-ui-component-container">
                <?= $block->getBlockHtml('formkey') ?>
                <?= $block->getBlockHtml('seller.formkey') ?>
                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
                <input type="hidden" name="is_threshold" value="1">
                
                <!-- Set default values for threshold mode -->
                <input type="hidden" name="courier_name" value="Free Shipping">
                <input type="hidden" name="service_type" value="free_shipping">
                <input type="hidden" name="weight" value="999999">
                <input type="hidden" name="shipping_price" value="0">
                <input type="hidden" name="free_shipping" value="1">
                <input type="hidden" name="packing_time" value="0">
                <input type="hidden" name="delivery_time" value="0">
                <input type="hidden" name="total_lead_time" value="0">
                <input type="hidden" name="return_address_id" value="0">
                
                <div class="page-main-actions">
                    <div class="page-actions-placeholder"></div>
                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Free Shipping Threshold")); ?>">
                            <div class="page-actions-buttons">
                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
                                data-ui-id="back-button">
                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
                                </button>
                                <button id="save"
                                title="<?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?>" type="submit"
                                class="action- scalable save primary ui-button ui-widget
                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
                                wk-ui-grid-btn-primary"
                                data-form-role="save"
                                data-ui-id="save-button" role="button" aria-disabled="false">
                                    <span class="ui-button-text">
                                        <span><?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="field required">
                    <label class="label" for="countries">
                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
                    </label>
                    <div class="tooltip">
                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where free shipping applies')) ?></span>
                    </div>
                    <div class="control">
                        <?php echo $countriesListHtml; ?>
                    </div>
                </div>
                
                <div class="field required">
                    <label for="min_order_amount" class="label">
                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
                    </label>
                    <div class="tooltip">
                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
                     </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-number validate-zero-or-greater" 
                        name="min_order_amount"
                        data-validate="{required:true, 'validate-number':true, 'validate-zero-or-greater':true}" 
                        title="<?= $escaper->escapeHtml(__("Minimum Order Amount")); ?>"
                        id="min_order_amount" 
                        value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getMinOrderAmount())) ?>">
                    </div>
                </div>
            </div>
        </form>
    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>

<script type="text/x-magento-init">
{
    "#form-save-threshold": {
        "Coditron_CustomShippingRate/js/threshold-form": {
            "backUrl": "<?= $escaper->escapeJs($backUrl) ?>"
        }
    }
}
</script>

<style>
    .select2-search__field {
        height: auto !important;
    }
</style>
