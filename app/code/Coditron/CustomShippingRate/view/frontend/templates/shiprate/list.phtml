<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<div class="wk-mpsellercategory-container">
    <!-- Shipping Methods Section -->
    <div class="wk-mp-section">
        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Shipping Methods')) ?></h3>
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
                        data-ui-id="add-button">
                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="shipping-methods-grid">
            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
        </div>
    </div>

    <!-- Free Shipping Thresholds Section -->
    <div class="wk-mp-section" style="margin-top: 40px;">
        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
        <div class="page-main-actions">
            <div class="page-actions-placeholder"></div>
            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                    <div class="page-actions-buttons">
                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                        onclick="location.href
                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['is_threshold' => 1]))?>';"
                        data-ui-id="add-threshold-button">
                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div id="threshold-grid">
            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
        </div>
    </div>
</div>
