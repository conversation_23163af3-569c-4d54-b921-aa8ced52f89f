<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>coditron_customshippingrate_shiptablerates_columns</spinner>
		<deps>
			<dep>coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="add">
				<url path="*/*/new"/>
				<class>primary</class>
				<label translate="true">Add new Table Rates</label>
			</button>
		</buttons>
	</settings>
	<dataSource name="coditron_customshippingrate_shiptablerates_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">shiptablerates_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Coditron_CustomShippingRate::ShipTableRates</aclResource>
		<dataProvider name="coditron_customshippingrate_shiptablerates_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>shiptablerates_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="coditron_customshippingrate_shiptablerates_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">shiptablerates_id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" xsi:type="url" path="coditron_customshippingrate/ShipTableRates/inlineEdit"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_listing.coditron_customshippingrate_shiptablerates_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>shiptablerates_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="shiptablerates_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="seller_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Seller Id</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="countries">
			<settings>
				<filter>text</filter>
				<label translate="true">Countries</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="courier_name">
			<settings>
				<filter>text</filter>
				<label translate="true">Courier Name</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="weight">
			<settings>
				<filter>text</filter>
				<label translate="true">Weight</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="shipping_price">
			<settings>
				<filter>text</filter>
				<label translate="true">Shipping Price</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ShipTableRatesActions">
			<settings>
				<indexField>shiptablerates_id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
	</columns>
</listing>
