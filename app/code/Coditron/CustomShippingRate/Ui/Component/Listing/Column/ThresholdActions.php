<?php
/**
 * Actions column for Free Shipping Threshold grid
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\Component\Listing\Column;

class ThresholdActions extends ShipTableRatesActions
{

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $name = $this->getData('name');
                if (isset($item['shiptablerates_id'])) {
                    $item[$name]['edit'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'coditron_customshippingrate/shiptablerates/edit',
                            ['shiptablerates_id' => $item['shiptablerates_id'], 'is_threshold' => 1]
                        ),
                        'label' => __('Edit')
                    ];
                    $item[$name]['delete'] = [
                        'href' => $this->urlBuilder->getUrl(
                            static::URL_PATH_DELETE,
                            ['shiptablerates_id' => $item['shiptablerates_id']]
                        ),
                        'label' => __('Delete'),
                        'confirm' => [
                            'title' => __('Delete Threshold'),
                            'message' => __('Are you sure you want to delete this free shipping threshold?')
                        ]
                    ];
                }
            }
        }

        return $dataSource;
    }
}
