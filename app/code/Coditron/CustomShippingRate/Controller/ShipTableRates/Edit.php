<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Shipping Rate Edit action.
     * Handles both regular shipping methods and free shipping thresholds.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $isThreshold = $this->getRequest()->getParam('is_threshold', false);

        $id = $this->getRequest()->getParam('id') ?: $this->getRequest()->getParam('shiptablerates_id');
        if ($id && !$this->getRequest()->getParam('shiptablerates_id')) {
            $this->getRequest()->setParam('shiptablerates_id', $id);
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            if ($isThreshold) {
                $resultPage->addHandle('mpsellership_layout2_threshold_edit');
            } else {
                $resultPage->addHandle('mpsellership_layout2_rate_edit');
            }
        }

        if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
            $sellerShiprate = $this->getSellerShiprate();
            if (empty($sellerShiprate->getShiptableratesId())) {
                $errorMessage = $isThreshold ? "Free shipping threshold does not exist" : "Shipping method does not exist";
                $this->messageManager->addError($errorMessage);
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/manage',
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }

            $title = $isThreshold ? "Edit Free Shipping Threshold" : $sellerShiprate->getCourierName();
        } else {
            $title = $isThreshold ? "New Free Shipping Threshold" : "New Shipping Method";
        }

        $resultPage->getConfig()->getTitle()->set(__($title));
        return $resultPage;
    }
}
